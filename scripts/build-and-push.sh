#!/bin/bash

# Build and Push Docker Images for Production
# Usage: ./scripts/build-and-push.sh [your-dockerhub-username] [tag]

set -e

# Default values
DOCKER_USERNAME=${1:-"your-dockerhub-username"}
TAG=${2:-"latest"}
PROJECT_NAME="sumopod"

echo "🚀 Building and pushing Docker images for $PROJECT_NAME"
echo "📦 Docker username: $DOCKER_USERNAME"
echo "🏷️  Tag: $TAG"

# Build images
echo "🔨 Building server image..."
docker build -f server/Dockerfile -t $DOCKER_USERNAME/$PROJECT_NAME-server:$TAG .

echo "🔨 Building client image..."
docker build -f client/Dockerfile -t $DOCKER_USERNAME/$PROJECT_NAME-client:$TAG .

# Push images
echo "📤 Pushing server image..."
docker push $DOCKER_USERNAME/$PROJECT_NAME-server:$TAG

echo "📤 Pushing client image..."
docker push $DOCKER_USERNAME/$PROJECT_NAME-client:$TAG

echo "✅ All images built and pushed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Update your Cloud Run services to use these new images:"
echo "   - Server: $DOCKER_USERNAME/$PROJECT_NAME-server:$TAG"
echo "   - Client: $DOCKER_USERNAME/$PROJECT_NAME-client:$TAG"
echo ""
echo "2. Update environment variables in Cloud Run with production values"
echo "3. Deploy the services"
